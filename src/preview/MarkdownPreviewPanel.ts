/**
 * Markdown预览面板
 * 
 * 负责管理VSCode Webview预览面板的生命周期和内容渲染
 * 这是重构后的核心预览组件，替代了原有的浏览器预览方式
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

import * as vscode from 'vscode';
import * as path from 'path';
import { MarkdownProcessor } from '../markdown/MarkdownProcessor';
import { TocProvider } from './TocProvider';
import { ConfigurationManager } from '../config/ConfigurationManager';
import { Logger } from '../utils/logger';

/**
 * Markdown预览面板类
 * 
 * 管理VSCode Webview面板，提供Markdown内容的实时预览功能
 */
export class MarkdownPreviewPanel {
  private static instance: MarkdownPreviewPanel;
  private panel: vscode.WebviewPanel | undefined;
  private currentDocument: vscode.TextDocument | undefined;
  private markdownProcessor: MarkdownProcessor;
  private tocProvider: TocProvider;
  private configManager: ConfigurationManager;
  private logger: Logger;
  private disposables: vscode.Disposable[] = [];
  private debugToolsVisible: boolean = false;

  /**
   * 私有构造函数
   */
  private constructor() {
    this.markdownProcessor = new MarkdownProcessor();
    this.tocProvider = new TocProvider();
    this.configManager = ConfigurationManager.getInstance();
    this.logger = Logger.getInstance();
  }

  /**
   * 获取预览面板实例（单例模式）
   * @returns MarkdownPreviewPanel实例
   */
  public static getInstance(): MarkdownPreviewPanel {
    if (!MarkdownPreviewPanel.instance) {
      MarkdownPreviewPanel.instance = new MarkdownPreviewPanel();
    }
    return MarkdownPreviewPanel.instance;
  }

  /**
   * 显示预览面板
   * @param document 要预览的Markdown文档
   * @param viewColumn 显示位置
   */
  public async show(document: vscode.TextDocument, viewColumn?: vscode.ViewColumn): Promise<void> {
    this.logger.info(`显示预览面板: ${path.basename(document.fileName)}`);
    
    try {
      // 如果面板不存在，创建新面板
      if (!this.panel) {
        await this.createPanel(viewColumn);
      }

      // 更新当前文档
      this.currentDocument = document;

      // 更新内容
      await this.updateContent();

      // 显示面板
      this.panel!.reveal(viewColumn);

    } catch (error) {
      this.logger.error('显示预览面板失败', error);
      throw error;
    }
  }

  /**
   * 创建Webview面板
   * @param viewColumn 显示位置
   */
  private async createPanel(viewColumn?: vscode.ViewColumn): Promise<void> {
    const config = this.configManager.getPreviewConfig();
    const column = viewColumn || (config.defaultView === 'side' ? vscode.ViewColumn.Beside : vscode.ViewColumn.Active);

    this.panel = vscode.window.createWebviewPanel(
      'markdownPreview',
      'Markdown预览',
      column,
      {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [
          vscode.Uri.file(path.join(__dirname, '..', '..', 'media')),
          vscode.Uri.file(path.join(__dirname, '..', '..', 'node_modules'))
        ]
      }
    );

    // 设置面板图标
    this.panel.iconPath = {
      light: vscode.Uri.file(path.join(__dirname, '..', '..', 'images', 'icon-light.svg')),
      dark: vscode.Uri.file(path.join(__dirname, '..', '..', 'images', 'icon-dark.svg'))
    };

    // 设置事件监听器
    this.setupEventListeners();

    this.logger.info('Webview面板已创建');
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (!this.panel) return;

    // 面板关闭事件
    this.panel.onDidDispose(() => {
      this.logger.info('预览面板已关闭');
      this.dispose();
    }, null, this.disposables);

    // 面板可见性变化事件
    this.panel.onDidChangeViewState(e => {
      this.logger.debug(`面板可见性变化: ${e.webviewPanel.visible}`);
    }, null, this.disposables);

    // Webview消息处理
    this.panel.webview.onDidReceiveMessage(
      message => this.handleWebviewMessage(message),
      null,
      this.disposables
    );
  }

  /**
   * 处理来自Webview的消息
   * @param message 消息对象
   */
  private async handleWebviewMessage(message: any): Promise<void> {
    this.logger.debug('收到Webview消息', message);

    switch (message.type) {
      case 'ready':
        // Webview已准备就绪，更新内容
        await this.updateContent();
        break;

      case 'click':
        // 处理点击事件
        await this.handleClick(message);
        break;

      case 'scroll':
        // 处理滚动事件
        this.handleScroll(message);
        break;

      case 'toc-click':
        // 处理目录点击事件
        await this.handleTocClick(message);
        break;

      case 'toc-toggle':
        // 处理目录折叠/展开事件
        this.handleTocToggle(message);
        break;

      case 'debug-info':
        // 处理调试信息请求
        this.handleDebugInfo(message);
        break;

      default:
        this.logger.warn(`未知的消息类型: ${message.type}`);
    }
  }

  /**
   * 更新预览内容
   */
  public async updateContent(): Promise<void> {
    if (!this.panel || !this.currentDocument) {
      return;
    }

    const timer = this.logger.createTimer('updateContent');

    try {
      const content = this.currentDocument.getText();
      const html = this.markdownProcessor.convertToHtml(content);
      const toc = this.tocProvider.generateToc(this.currentDocument);

      // 生成完整的HTML内容
      const webviewHtml = this.getWebviewContent(html, toc);
      
      // 更新Webview内容
      this.panel.webview.html = webviewHtml;
      
      // 更新面板标题
      this.panel.title = `预览: ${path.basename(this.currentDocument.fileName)}`;

      this.logger.debug('预览内容已更新');
    } catch (error) {
      this.logger.error('更新预览内容失败', error);
      
      // 显示错误页面
      this.panel.webview.html = this.getErrorContent(error as Error);
    } finally {
      timer.end();
    }
  }

  /**
   * 生成Webview HTML内容
   * @param html Markdown转换后的HTML
   * @param toc 目录结构
   * @returns 完整的HTML内容
   */
  private getWebviewContent(html: string, toc: any[]): string {
    const config = this.configManager.getConfig();
    const nonce = this.generateNonce();

    // 获取资源URI
    const styleUri = this.panel!.webview.asWebviewUri(
      vscode.Uri.file(path.join(__dirname, '..', '..', 'media', 'preview.css'))
    );
    
    const scriptUri = this.panel!.webview.asWebviewUri(
      vscode.Uri.file(path.join(__dirname, '..', '..', 'media', 'preview.js'))
    );

    const mermaidUri = this.panel!.webview.asWebviewUri(
      vscode.Uri.file(path.join(__dirname, '..', '..', 'node_modules', 'mermaid', 'dist', 'mermaid.min.js'))
    );

    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${this.panel!.webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}'; img-src ${this.panel!.webview.cspSource} https: data:;">
    <title>Markdown预览</title>
    <link rel="stylesheet" href="${styleUri}">
    <style nonce="${nonce}">
        :root {
            --font-size: ${config.theme.fontSize}px;
            --font-family: ${config.theme.fontFamily || 'inherit'};
            --line-height: ${config.theme.lineHeight};
        }
        body {
            font-size: var(--font-size);
            font-family: var(--font-family);
            line-height: var(--line-height);
        }
        .debug-tools {
            display: ${this.debugToolsVisible ? 'block' : 'none'};
        }
    </style>
</head>
<body>
    <div class="container">
        ${config.preview.showToc ? this.renderTocContainer(toc) : ''}
        <div class="content-container">
            <div class="debug-tools">
                <div class="debug-panel">
                    <h3>调试信息</h3>
                    <div class="debug-info">
                        <p>文档: ${this.currentDocument ? path.basename(this.currentDocument.fileName) : '无'}</p>
                        <p>行数: ${this.currentDocument ? this.currentDocument.lineCount : 0}</p>
                        <p>字符数: ${this.currentDocument ? this.currentDocument.getText().length : 0}</p>
                    </div>
                </div>
            </div>
            <div class="markdown-content">
                ${html}
            </div>
        </div>
    </div>
    
    <script nonce="${nonce}" src="${mermaidUri}"></script>
    <script nonce="${nonce}" src="${scriptUri}"></script>
    <script nonce="${nonce}">
        // 初始化配置
        window.markdownLiveSyncConfig = ${JSON.stringify(config)};
        
        // 发送就绪消息
        vscode.postMessage({ type: 'ready' });
    </script>
</body>
</html>`;
  }

  /**
   * 渲染目录容器
   * @param toc 目录结构
   * @returns 目录HTML
   */
  private renderTocContainer(toc: any[]): string {
    return `
      <div class="toc-container">
        <div class="toc-header">
          <h3>目录</h3>
          <button class="toc-collapse-all" title="全部折叠">⊟</button>
          <button class="toc-expand-all" title="全部展开">⊞</button>
        </div>
        <div class="toc-content">
          ${this.tocProvider.renderToc(toc)}
        </div>
      </div>
    `;
  }

  /**
   * 生成错误页面内容
   * @param error 错误对象
   * @returns 错误页面HTML
   */
  private getErrorContent(error: Error): string {
    return `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>预览错误</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .error { color: #d32f2f; background: #ffebee; padding: 15px; border-radius: 4px; }
        .error-title { font-weight: bold; margin-bottom: 10px; }
        .error-message { margin-bottom: 10px; }
        .error-stack { font-family: monospace; font-size: 12px; background: #f5f5f5; padding: 10px; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="error">
        <div class="error-title">预览渲染失败</div>
        <div class="error-message">${error.message}</div>
        ${error.stack ? `<div class="error-stack">${error.stack}</div>` : ''}
    </div>
</body>
</html>`;
  }

  /**
   * 生成随机nonce
   * @returns nonce字符串
   */
  private generateNonce(): string {
    let text = '';
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 32; i++) {
      text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
  }

  /**
   * 处理点击事件
   * @param message 消息对象
   */
  private async handleClick(message: any): Promise<void> {
    // 实现点击处理逻辑
    this.logger.debug('处理点击事件', message);
  }

  /**
   * 处理滚动事件
   * @param message 消息对象
   */
  private handleScroll(message: any): void {
    // 实现滚动处理逻辑
    this.logger.debug('处理滚动事件', message);
  }

  /**
   * 处理目录点击事件
   * @param message 消息对象
   */
  private async handleTocClick(message: any): Promise<void> {
    if (!this.currentDocument || !message.line) {
      return;
    }

    try {
      // 跳转到指定行
      const editor = await vscode.window.showTextDocument(this.currentDocument);
      const position = new vscode.Position(message.line - 1, 0);
      editor.selection = new vscode.Selection(position, position);
      editor.revealRange(new vscode.Range(position, position), vscode.TextEditorRevealType.InCenter);
      
      this.logger.debug(`跳转到行: ${message.line}`);
    } catch (error) {
      this.logger.error('跳转失败', error);
    }
  }

  /**
   * 处理目录折叠/展开事件
   * @param message 消息对象
   */
  private handleTocToggle(message: any): void {
    // 实现目录折叠状态管理
    this.logger.debug('目录折叠状态变更', message);
  }

  /**
   * 处理调试信息请求
   * @param message 消息对象
   */
  private handleDebugInfo(message: any): void {
    // 发送调试信息到Webview
    this.panel?.webview.postMessage({
      type: 'debug-response',
      data: {
        document: this.currentDocument ? path.basename(this.currentDocument.fileName) : null,
        lineCount: this.currentDocument?.lineCount || 0,
        config: this.configManager.getConfig()
      }
    });
  }

  /**
   * 同步光标位置
   * @param position 光标位置
   */
  public syncCursorPosition(position: vscode.Position): void {
    if (!this.panel || !this.configManager.getPreviewConfig().syncScroll) {
      return;
    }

    this.panel.webview.postMessage({
      type: 'sync-cursor',
      line: position.line + 1
    });
  }

  /**
   * 更新活动文档
   * @param document 新的活动文档
   */
  public updateActiveDocument(document: vscode.TextDocument): void {
    if (this.currentDocument?.uri.toString() !== document.uri.toString()) {
      this.currentDocument = document;
      this.updateContent();
    }
  }

  /**
   * 检查是否为当前文档
   * @param document 要检查的文档
   * @returns 是否为当前文档
   */
  public isCurrentDocument(document: vscode.TextDocument): boolean {
    return this.currentDocument?.uri.toString() === document.uri.toString();
  }

  /**
   * 关闭指定文档的预览
   * @param uri 文档URI
   */
  public closePreviewForDocument(uri: vscode.Uri): void {
    if (this.currentDocument?.uri.toString() === uri.toString()) {
      this.dispose();
    }
  }

  /**
   * 切换调试工具显示状态
   */
  public toggleDebugTools(): void {
    this.debugToolsVisible = !this.debugToolsVisible;
    this.updateContent();
    this.logger.info(`调试工具${this.debugToolsVisible ? '已显示' : '已隐藏'}`);
  }

  /**
   * 配置变更处理
   */
  public onConfigurationChanged(): void {
    this.logger.info('配置已变更，更新预览');
    this.updateContent();
  }

  /**
   * 检查面板是否可见
   * @returns 面板可见状态
   */
  public isVisible(): boolean {
    return this.panel?.visible || false;
  }

  /**
   * 销毁预览面板
   */
  public dispose(): void {
    this.logger.info('销毁预览面板');
    
    // 清理事件监听器
    this.disposables.forEach(d => d.dispose());
    this.disposables = [];

    // 销毁面板
    if (this.panel) {
      this.panel.dispose();
      this.panel = undefined;
    }

    // 清理当前文档引用
    this.currentDocument = undefined;
  }
}
