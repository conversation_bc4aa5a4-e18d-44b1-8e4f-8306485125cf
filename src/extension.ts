/**
 * Markdown LiveSync 插件入口
 *
 * 重构后的新版本，使用VSCode内置预览面板替代浏览器预览
 *
 * <AUTHOR>
 * @version 1.0.0
 */

import * as vscode from 'vscode';
import { Extension } from './core/extension';
import { ConfigurationManager } from './config/ConfigurationManager';
import { Logger } from './utils/logger';

// 全局插件实例
let extension: Extension | undefined;

/**
 * 插件激活函数
 * @param context VSCode扩展上下文
 */
export function activate(context: vscode.ExtensionContext) {
  try {
    // 初始化日志器
    const logger = Logger.getInstance();

    // 初始化配置管理器
    const configManager = ConfigurationManager.getInstance();

    // 设置调试模式
    logger.setDebugEnabled(configManager.isDebugEnabled());

    logger.info('Markdown LiveSync 插件开始激活...');

    // 创建并激活插件实例
    extension = Extension.getInstance(context);
    extension.activate();

    logger.info('Markdown LiveSync 插件激活成功');
  } catch (error) {
    console.error('Markdown LiveSync 插件激活失败:', error);
    vscode.window.showErrorMessage(`Markdown LiveSync 插件激活失败: ${(error as Error).message}`);
  }
}

/**
 * 插件停用函数
 */
export function deactivate() {
  try {
    const logger = Logger.getInstance();
    logger.info('Markdown LiveSync 插件开始停用...');

    if (extension) {
      extension.deactivate();
      extension = undefined;
    }

    logger.info('Markdown LiveSync 插件停用完成');
    logger.dispose();
  } catch (error) {
    console.error('Markdown LiveSync 插件停用时发生错误:', error);
  }
}
