/**
 * Markdown处理器
 *
 * 负责将Markdown文本转换为HTML，支持各种扩展语法
 * 包括代码高亮、Mermaid图表、数学公式等
 * 这是重构后的新版本，适配VSCode Webview环境
 *
 * <AUTHOR>
 * @version 1.0.0
 */

import * as vscode from 'vscode';
import * as MarkdownIt from 'markdown-it';
import * as path from 'path';
import { lineNumberPlugin } from './lineNumberPlugin';
import { mermaidPlugin } from './mermaidPlugin';
import { ConfigurationManager } from '../config/ConfigurationManager';
import { Logger } from '../utils/logger';

// 保留旧的目录项接口以兼容现有代码
export interface TocItem {
  level: number;
  text: string;
  slug?: string; // 现在是可选的
}

/**
 * Markdown处理器类
 *
 * 提供Markdown到HTML的转换功能，支持多种扩展语法
 */
export class MarkdownProcessor {
  private md: MarkdownIt;
  private configManager: ConfigurationManager;
  private logger: Logger;
  private cache: Map<string, string> = new Map();

  /**
   * 构造函数
   */
  constructor() {
    this.configManager = ConfigurationManager.getInstance();
    this.logger = Logger.getInstance();
    this.initializeMarkdownIt();
  }

  /**
   * 初始化MarkdownIt实例
   */
  private initializeMarkdownIt(): void {
    this.md = new MarkdownIt({
      html: true,
      linkify: true,
      typographer: true,
      highlight: (str: string, lang: string) => {
        // 代码高亮处理 - 在Webview环境中使用简单的高亮
        return `<pre class="hljs"><code class="language-${lang}">${this.md.utils.escapeHtml(str)}</code></pre>`;
      }
    });

    // 配置插件
    this.configurePlugins();
  }

  /**
   * 配置Markdown-it插件
   */
  private configurePlugins(): void {
    try {
      // 添加行号插件
      lineNumberPlugin(this.md);

      // 添加Mermaid插件
      mermaidPlugin(this.md);

      // 可以在这里添加更多插件
      this.addTablePlugin();
      this.addTaskListPlugin();
      this.addMathPlugin();

      this.logger.debug('Markdown插件配置完成');
    } catch (error) {
      this.logger.error('配置Markdown插件失败', error);
    }
  }

  /**
   * 添加表格插件
   */
  private addTablePlugin(): void {
    // 简单的表格支持
    this.md.renderer.rules.table_open = () => '<table class="markdown-table">\n';
    this.md.renderer.rules.table_close = () => '</table>\n';
    this.md.renderer.rules.thead_open = () => '<thead>\n';
    this.md.renderer.rules.thead_close = () => '</thead>\n';
    this.md.renderer.rules.tbody_open = () => '<tbody>\n';
    this.md.renderer.rules.tbody_close = () => '</tbody>\n';
    this.md.renderer.rules.tr_open = () => '<tr>\n';
    this.md.renderer.rules.tr_close = () => '</tr>\n';
    this.md.renderer.rules.th_open = () => '<th>';
    this.md.renderer.rules.th_close = () => '</th>\n';
    this.md.renderer.rules.td_open = () => '<td>';
    this.md.renderer.rules.td_close = () => '</td>\n';
  }

  /**
   * 添加任务列表插件
   */
  private addTaskListPlugin(): void {
    // 简单的任务列表支持
    const originalListItemOpen = this.md.renderer.rules.list_item_open ||
      ((tokens, idx, options, env, self) => self.renderToken(tokens, idx, options));

    this.md.renderer.rules.list_item_open = (tokens, idx, options, env, self) => {
      const token = tokens[idx];
      const nextToken = tokens[idx + 1];

      if (nextToken && nextToken.content) {
        const taskMatch = nextToken.content.match(/^\[([x\s])\]\s*/);
        if (taskMatch) {
          const checked = taskMatch[1] === 'x';
          nextToken.content = nextToken.content.replace(/^\[([x\s])\]\s*/, '');
          return `<li class="task-list-item"><input type="checkbox" ${checked ? 'checked' : ''} disabled> `;
        }
      }

      return originalListItemOpen(tokens, idx, options, env, self);
    };
  }

  /**
   * 添加数学公式插件
   */
  private addMathPlugin(): void {
    // 简单的数学公式支持（使用KaTeX或MathJax的占位符）
    this.md.renderer.rules.math_inline = (tokens, idx) => {
      return `<span class="math-inline" data-math="${this.md.utils.escapeHtml(tokens[idx].content)}">$${tokens[idx].content}$</span>`;
    };

    this.md.renderer.rules.math_block = (tokens, idx) => {
      return `<div class="math-block" data-math="${this.md.utils.escapeHtml(tokens[idx].content)}">$$${tokens[idx].content}$$</div>`;
    };
  }

  /**
   * 将Markdown文本转换为HTML
   * @param markdown Markdown文本
   * @param documentUri 文档URI（可选，用于解析相对路径）
   * @returns HTML字符串
   */
  public convertToHtml(markdown: string, documentUri?: vscode.Uri): string {
    const timer = this.logger.createTimer('convertToHtml');

    try {
      // 检查缓存
      const cacheKey = this.getCacheKey(markdown);
      if (this.cache.has(cacheKey)) {
        this.logger.debug('使用缓存的HTML内容');
        return this.cache.get(cacheKey)!;
      }

      // 渲染Markdown
      let html = this.md.render(markdown);

      // 安全处理
      html = this.sanitizeHtml(html);

      // 处理图片路径
      if (documentUri) {
        html = this.resolveImagePaths(html, documentUri);
      }

      // 添加行号指示器
      html = this.addLineIndicators(html, markdown);

      // 缓存结果
      this.updateCache(cacheKey, html);

      this.logger.debug('Markdown转换完成');
      return html;

    } catch (error) {
      this.logger.error('Markdown转换失败', error);
      return this.getErrorHtml(error as Error);
    } finally {
      timer.end();
    }
  }

  /**
   * 添加行号指示器
   * @param html HTML内容
   * @param markdown 原始Markdown
   * @returns 带行号指示器的HTML
   */
  private addLineIndicators(html: string, markdown: string): string {
    const lines = markdown.split('\n');
    const totalLines = lines.length;

    let result = '<div class="markdown-content">';

    // 添加行号指示器容器
    result += '<div class="line-indicators" style="display:none;">';
    for (let i = 1; i <= totalLines; i++) {
      result += `<div id="indicator-${i}" class="line-indicator"></div>`;
    }
    result += '</div>';

    // 添加内容
    result += html;
    result += '</div>';

    return result;
  }

  /**
   * 安全处理HTML内容
   * @param html HTML内容
   * @returns 安全的HTML内容
   */
  private sanitizeHtml(html: string): string {
    // 移除危险的脚本和事件处理器
    let sanitized = html;

    // 移除script标签
    sanitized = sanitized.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');

    // 移除事件处理属性
    sanitized = sanitized.replace(/\s+on\w+\s*=\s*(['"])[^'"]*\1/gi, '');

    // 移除javascript: 链接
    sanitized = sanitized.replace(/href\s*=\s*(['"])javascript:[^'"]*\1/gi, '');

    // 处理SVG安全
    sanitized = this.sanitizeSvg(sanitized);

    return sanitized;
  }

  /**
   * 处理SVG安全
   * @param html HTML内容
   * @returns 安全的HTML内容
   */
  private sanitizeSvg(html: string): string {
    // 移除SVG中的危险属性
    let sanitized = html.replace(
      /<svg\s+([^>]*)(\/?)>/gi,
      (_match, attributes, selfClosing) => {
        // 移除事件处理属性
        const safeAttributes = attributes.replace(/\s+on\w+\s*=\s*(['"])[^'"]*\1/gi, '');
        return `<svg ${safeAttributes}${selfClosing}>`;
      }
    );

    // 移除SVG中的script标签
    sanitized = sanitized.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');

    // 移除foreignObject标签
    sanitized = sanitized.replace(/<foreignObject\b[^<]*(?:(?!<\/foreignObject>)<[^<]*)*<\/foreignObject>/gi, '');

    return sanitized;
  }

  /**
   * 解析图片路径
   * @param html HTML内容
   * @param documentUri 文档URI
   * @returns 处理后的HTML
   */
  private resolveImagePaths(html: string, documentUri: vscode.Uri): string {
    const documentDir = path.dirname(documentUri.fsPath);

    return html.replace(
      /<img\s+([^>]*?)src="([^"]+)"([^>]*?)>/g,
      (_match, beforeSrc, imgPath, afterSrc) => {
        if (imgPath.startsWith('http://') || imgPath.startsWith('https://') || imgPath.startsWith('data:')) {
          return _match;
        }

        // 转换为绝对路径
        const absolutePath = path.resolve(documentDir, imgPath);
        const fileUri = vscode.Uri.file(absolutePath);

        // 在Webview中使用vscode-resource协议
        return `<img ${beforeSrc}src="${fileUri.toString()}"${afterSrc}>`;
      }
    );
  }

  /**
   * 生成缓存键
   * @param content 内容
   * @returns 缓存键
   */
  private getCacheKey(content: string): string {
    // 简单的哈希函数
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString();
  }

  /**
   * 更新缓存
   * @param key 缓存键
   * @param value 缓存值
   */
  private updateCache(key: string, value: string): void {
    const config = this.configManager.getPerformanceConfig();

    if (this.cache.size >= config.cacheSize) {
      // 移除最旧的缓存项
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }

    this.cache.set(key, value);
  }

  /**
   * 生成错误HTML
   * @param error 错误对象
   * @returns 错误HTML
   */
  private getErrorHtml(error: Error): string {
    return `
      <div class="markdown-error">
        <h3>Markdown渲染错误</h3>
        <p><strong>错误信息:</strong> ${this.md.utils.escapeHtml(error.message)}</p>
        <details>
          <summary>详细信息</summary>
          <pre>${this.md.utils.escapeHtml(error.stack || '无堆栈信息')}</pre>
        </details>
      </div>
    `;
  }

  /**
   * 生成目录结构（保留兼容性）
   *
   * 这个函数解析Markdown文本，提取所有标题并生成目录结构。
   * 它会忽略代码块中的内容，只处理真正的标题。
   *
   * @param {string} markdown - Markdown文本
   * @returns {TocItem[]} - 目录项数组
   */
  public generateToc(markdown: string): TocItem[] {
    const toc: TocItem[] = [];
    const lines = markdown.split('\n');

    // 正则表达式匹配标题行
    const headerRegex = /^(#{1,6})\s+(.+)$/;

    // 跟踪是否在代码块内
    let inCodeBlock = false;
    // 跟踪是否在缩进代码块内
    let inIndentedCodeBlock = false;
    // 跟踪连续空行数量（用于检测缩进代码块的结束）
    let consecutiveEmptyLines = 0;
    // 跟踪前一行是否是列表项
    let previousLineIsList = false;

    // 逐行处理Markdown
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmedLine = line.trim();

      // 检查是否是空行
      if (trimmedLine === '') {
        consecutiveEmptyLines++;
        // 如果有连续两个空行，认为缩进代码块结束
        if (consecutiveEmptyLines >= 2) {
          inIndentedCodeBlock = false;
        }
        continue;
      } else {
        consecutiveEmptyLines = 0;
      }

      // 检查是否是围栏式代码块的开始或结束
      const fencedCodeBlockMatch = line.match(/^(\s*)(`{3,}|~{3,})(\w*)/);

      if (fencedCodeBlockMatch) {
        // 切换代码块状态
        inCodeBlock = !inCodeBlock;
        continue;
      }

      // 检查是否是缩进式代码块的开始
      if (!inCodeBlock && !inIndentedCodeBlock && line.startsWith('    ') && !previousLineIsList) {
        inIndentedCodeBlock = true;
        continue;
      }

      // 检查是否是列表项
      previousLineIsList = /^\s*[\*\-\+]|\d+\.\s/.test(line);

      // 如果在任何类型的代码块内，跳过此行
      if (inCodeBlock || inIndentedCodeBlock) {
        continue;
      }

      // 匹配标题行
      const match = line.match(headerRegex);
      if (match) {
        const level = match[1].length; // 标题级别 (# = 1, ## = 2, 等)
        const text = match[2].trim();

        // 检查是否是HTML注释中的标题
        const isInHtmlComment = this.isLineInHtmlComment(lines, i);

        // 只有不在HTML注释中的标题才添加到目录
        if (!isInHtmlComment) {
          // 不再使用slug，只使用level和text
          toc.push({ level, text });
        }
      }
    }

    return toc;
  }

  /**
   * 清空缓存
   */
  public clearCache(): void {
    this.cache.clear();
    this.logger.debug('Markdown缓存已清空');
  }

  /**
   * 获取缓存统计信息
   * @returns 缓存统计
   */
  public getCacheStats(): { size: number; maxSize: number } {
    const config = this.configManager.getPerformanceConfig();
    return {
      size: this.cache.size,
      maxSize: config.cacheSize
    };
  }

  /**
   * 检查行是否在HTML注释中
   *
   * @param {string[]} lines - 所有行
   * @param {number} lineIndex - 当前行索引
   * @returns {boolean} - 是否在HTML注释中
   */
  private isLineInHtmlComment(lines: string[], lineIndex: number): boolean {
    // 向上查找HTML注释开始标记
    let commentStartFound = false;
    for (let i = lineIndex; i >= 0; i--) {
      if (lines[i].includes('<!--')) {
        commentStartFound = true;
        break;
      }
      if (lines[i].includes('-->')) {
        // 找到了结束标记，但没有找到开始标记，所以不在注释中
        return false;
      }
    }

    // 如果找到了开始标记，向下查找结束标记
    if (commentStartFound) {
      // 检查当前行是否包含结束标记
      if (lines[lineIndex].includes('-->')) {
        return false;
      }

      // 向下查找结束标记
      for (let i = lineIndex + 1; i < lines.length; i++) {
        if (lines[i].includes('-->')) {
          return true;
        }
      }
    }

    return commentStartFound;
  }

  /**
   * 处理文档中的图片路径，转换为绝对路径（保留兼容性）
   */
  public resolveImagePaths(html: string, documentUri: vscode.Uri): string {
    const documentDir = path.dirname(documentUri.fsPath);

    // 使用正则表达式替换相对图片路径
    return html.replace(
      /<img\s+src="([^"]+)"/g,
      (_match, imgPath) => {
        if (imgPath.startsWith('http://') || imgPath.startsWith('https://') || imgPath.startsWith('data:')) {
          // 已经是绝对路径或数据URI，不需要处理
          return _match;
        }

        // 转换为绝对路径
        const absolutePath = path.resolve(documentDir, imgPath);
        return `<img src="file://${absolutePath}"`;
      }
    );
  }
}
