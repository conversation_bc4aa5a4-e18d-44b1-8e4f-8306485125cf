/**
 * 日志工具类
 *
 * 提供统一的日志记录功能，支持不同级别的日志输出
 * 可以根据配置启用或禁用调试日志
 *
 * <AUTHOR>
 * @version 1.0.0
 */

import * as vscode from 'vscode';

/**
 * 日志级别枚举
 */
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

/**
 * 日志工具类
 *
 * 提供统一的日志记录接口，支持不同级别的日志输出
 */
export class Logger {
  private static instance: Logger;
  private outputChannel: vscode.OutputChannel;
  private debugEnabled: boolean = false;

  /**
   * 私有构造函数
   */
  private constructor() {
    this.outputChannel = vscode.window.createOutputChannel('Markdown LiveSync');
  }

  /**
   * 获取日志器实例（单例模式）
   * @returns Logger实例
   */
  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  /**
   * 设置调试模式
   * @param enabled 是否启用调试模式
   */
  public setDebugEnabled(enabled: boolean): void {
    this.debugEnabled = enabled;
    if (enabled) {
      this.info('调试模式已启用');
    }
  }

  /**
   * 记录调试信息
   * @param message 消息内容
   * @param data 附加数据
   */
  public debug(message: string, data?: any): void {
    if (this.debugEnabled) {
      this.log(LogLevel.DEBUG, message, data);
    }
  }

  /**
   * 记录信息
   * @param message 消息内容
   * @param data 附加数据
   */
  public info(message: string, data?: any): void {
    this.log(LogLevel.INFO, message, data);
  }

  /**
   * 记录警告
   * @param message 消息内容
   * @param data 附加数据
   */
  public warn(message: string, data?: any): void {
    this.log(LogLevel.WARN, message, data);
  }

  /**
   * 记录错误
   * @param message 消息内容
   * @param error 错误对象或附加数据
   */
  public error(message: string, error?: any): void {
    this.log(LogLevel.ERROR, message, error);
  }

  /**
   * 内部日志记录方法
   * @param level 日志级别
   * @param message 消息内容
   * @param data 附加数据
   */
  private log(level: LogLevel, message: string, data?: any): void {
    const timestamp = new Date().toISOString();
    const levelName = LogLevel[level];

    let logMessage = `[${timestamp}] [${levelName}] ${message}`;

    if (data !== undefined) {
      if (data instanceof Error) {
        logMessage += `\n错误详情: ${data.message}`;
        if (data.stack) {
          logMessage += `\n堆栈跟踪:\n${data.stack}`;
        }
      } else if (typeof data === 'object') {
        try {
          logMessage += `\n数据: ${JSON.stringify(data, null, 2)}`;
        } catch (e) {
          logMessage += `\n数据: [无法序列化的对象]`;
        }
      } else {
        logMessage += `\n数据: ${data}`;
      }
    }

    // 输出到控制台
    switch (level) {
      case LogLevel.DEBUG:
        console.debug(logMessage);
        break;
      case LogLevel.INFO:
        console.info(logMessage);
        break;
      case LogLevel.WARN:
        console.warn(logMessage);
        break;
      case LogLevel.ERROR:
        console.error(logMessage);
        break;
    }

    // 输出到VSCode输出面板
    this.outputChannel.appendLine(logMessage);
  }

  /**
   * 显示输出面板
   */
  public show(): void {
    this.outputChannel.show();
  }

  /**
   * 清空日志
   */
  public clear(): void {
    this.outputChannel.clear();
  }

  /**
   * 记录性能指标
   * @param operation 操作名称
   * @param startTime 开始时间
   * @param endTime 结束时间（可选，默认为当前时间）
   */
  public logPerformance(operation: string, startTime: number, endTime?: number): void {
    const end = endTime || Date.now();
    const duration = end - startTime;
    this.debug(`性能指标 - ${operation}: ${duration}ms`);
  }

  /**
   * 创建性能计时器
   * @param operation 操作名称
   * @returns 计时器对象
   */
  public createTimer(operation: string): { end: () => void } {
    const startTime = Date.now();
    return {
      end: () => this.logPerformance(operation, startTime)
    };
  }

  /**
   * 销毁日志器
   */
  public dispose(): void {
    this.outputChannel.dispose();
  }
}
