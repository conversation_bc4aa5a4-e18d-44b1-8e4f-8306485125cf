{"name": "markdown-livesync", "displayName": "Markdown LiveSync", "description": "实时同步Markdown编辑与VSCode内置预览，支持目录导航、光标同步和Mermaid图表渲染", "version": "1.0.0", "publisher": "hmslsky", "author": "hmslsky", "icon": "images/icon.png", "engines": {"vscode": "^1.60.0"}, "categories": ["Other"], "activationEvents": ["onLanguage:markdown"], "main": "./out/extension.js", "repository": {"type": "git", "url": "https://github.com/hmslsky/markdown-livesync.git"}, "contributes": {"commands": [{"command": "markdown-livesync.openPreview", "title": "Markdown LiveSync: 打开预览"}, {"command": "markdown-livesync.openPreviewToSide", "title": "Markdown LiveSync: 在侧边打开预览"}, {"command": "markdown-livesync.toggleDebugTools", "title": "Markdown LiveSync: 切换调试工具"}], "menus": {"editor/context": [{"when": "editorLangId == markdown", "command": "markdown-livesync.openPreview", "group": "navigation"}], "editor/title": [{"when": "editorLangId == markdown", "command": "markdown-livesync.openPreviewToSide", "group": "navigation"}]}, "keybindings": [{"command": "markdown-livesync.openPreview", "key": "ctrl+shift+v", "mac": "cmd+shift+v", "when": "editorLangId == markdown"}, {"command": "markdown-livesync.toggleDebugTools", "key": "ctrl+shift+d", "mac": "cmd+shift+d", "when": "editorLangId == markdown"}], "configuration": {"title": "Markdown LiveSync", "properties": {"markdown-livesync.preview": {"type": "object", "properties": {"defaultView": {"type": "string", "enum": ["side", "window"], "default": "side", "description": "预览面板的默认显示位置"}, "showToc": {"type": "boolean", "default": true, "description": "是否显示目录导航"}, "syncScroll": {"type": "boolean", "default": true, "description": "是否同步滚动"}, "highlightOnScroll": {"type": "boolean", "default": false, "description": "滚动到指定位置时是否高亮显示目标元素"}}}, "markdown-livesync.toc": {"type": "object", "properties": {"defaultCollapseLevel": {"type": "number", "default": 2, "description": "目录默认折叠级别"}, "showToggleButton": {"type": "boolean", "default": true, "description": "是否显示折叠按钮"}, "highlightCurrentItem": {"type": "boolean", "default": true, "description": "是否高亮当前目录项"}, "rememberCollapseState": {"type": "boolean", "default": true, "description": "是否记住目录折叠状态"}}}, "markdown-livesync.theme": {"type": "object", "properties": {"fontSize": {"type": "number", "default": 14, "description": "预览字体大小"}, "fontFamily": {"type": "string", "default": "", "description": "预览字体"}, "lineHeight": {"type": "number", "default": 1.6, "description": "行高"}}}, "markdown-livesync.performance": {"type": "object", "properties": {"chunkSize": {"type": "number", "default": 1000, "description": "分块渲染大小"}, "cacheSize": {"type": "number", "default": 100, "description": "缓存大小"}, "lazyLoad": {"type": "boolean", "default": true, "description": "是否启用懒加载"}}}, "markdown-livesync.debug": {"type": "boolean", "default": false, "description": "启用调试日志"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/glob": "^7.1.4", "@types/linkify-it": "^3.0.2", "@types/markdown-it": "^12.0.3", "@types/mocha": "^9.0.0", "@types/node": "14.x", "@types/vscode": "^1.60.0", "@typescript-eslint/eslint-plugin": "^5.1.0", "@typescript-eslint/parser": "^5.1.0", "@vscode/test-electron": "^1.6.2", "eslint": "^8.1.0", "glob": "^7.1.7", "mocha": "^11.2.2", "typescript": "^4.5.5"}, "dependencies": {"markdown-it": "^12.3.2"}}